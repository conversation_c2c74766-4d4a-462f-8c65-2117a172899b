{"name": "STM32G030", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "Application", "files": [], "folders": [{"name": "MDK-ARM", "files": [{"path": "startup_stm32g030xx.s"}], "folders": []}, {"name": "User", "files": [{"path": "../Src/main.c"}, {"path": "../Src/gpio.c"}, {"path": "../Src/adc.c"}, {"path": "../Src/dma.c"}, {"path": "../Src/tim.c"}, {"path": "../Src/usart.c"}, {"path": "../Src/stm32g0xx_it.c"}, {"path": "../Src/stm32g0xx_hal_msp.c"}, {"path": "../Src/key.c"}, {"path": "../Src/esp8266.c"}, {"path": "../Src/dht11.c"}], "folders": []}]}, {"name": "Drivers", "files": [], "folders": [{"name": "STM32G0xx_HAL_Driver", "files": [{"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_gpio.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_adc.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_adc_ex.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_ll_adc.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_rcc.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_rcc_ex.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_ll_rcc.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_flash.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_flash_ex.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_dma.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_dma_ex.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pwr.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_pwr_ex.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_cortex.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_exti.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_tim.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_tim_ex.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_uart.c"}, {"path": "../Drivers/STM32G0xx_HAL_Driver/Src/stm32g0xx_hal_uart_ex.c"}], "folders": []}, {"name": "CMSIS", "files": [{"path": "../Src/system_stm32g0xx.c"}], "folders": []}]}, {"name": "Function", "files": [{"path": "../Src/fs_crc.c"}, {"path": "../Src/fs_protocol.c"}, {"path": "../Src/bsp_esp8266.c"}, {"path": "../Src/bsp_lcd.c"}, {"path": "../Src/bsp_P9813.c"}, {"path": "../Src/bsp_softwareTimer.c"}, {"path": "../Src/bsp_ultrasonic.c"}, {"path": "../Src/app_system.c"}, {"path": "../Src/bsp_beep.c"}], "folders": []}, {"name": "::CMSIS", "files": [], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "02f24f41828070cdc443cd9498a698d8"}, "targets": {"STM32G030": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M0+", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x2000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "STLink", "uploadConfig": {"bin": "", "proType": "SWD", "resetMode": "default", "runAfterProgram": true, "speed": 4000, "address": "0x8000000", "elFile": "None", "optionBytes": ".eide/stm32g030.st.option.bytes.ini", "otherCmds": ""}, "uploadConfigMap": {"JLink": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}}, "custom_dep": {"name": "default", "incList": ["../Inc", "../Drivers/STM32G0xx_HAL_Driver/Inc", "../Drivers/STM32G0xx_HAL_Driver/Inc/Legacy", "../Drivers/CMSIS/Device/ST/STM32G0xx/Include", "../Drivers/CMSIS/Include", ".cmsis/include", "RTE/_STM32G030"], "libList": [], "defineList": ["USE_HAL_DRIVER", "STM32G030xx"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [{"name": "[Copy linker output for Keil User Commands]", "command": "mkdir ${KEIL_OUTPUT_DIR} & copy \"${OutDir}\\${ProjectName}.axf\" \"${KEIL_OUTPUT_DIR}\\${ProjectName}.axf\"", "disable": false, "abortAfterFailed": true}, {"name": "fromelf.exe --bin !L --output ./bin/WiFi-Client.bin", "command": "fromelf.exe --bin ${KEIL_OUTPUT_DIR}\\${ProjectName}.axf --output ./bin/WiFi-Client.bin", "disable": false, "abortAfterFailed": true}], "global": {"use-microLIB": true, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "all-warnings"}, "asm-compiler": {}, "linker": {"output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.5"}